# FILL OUT BELOW AND THEN RUN npm run distributeEnv.

#---@makeagent/ma-next---
NEXT_PUBLIC_SUPABASE_ANON_KEY=
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321 # https://<>.supabase.co
#---------------------------------


#---@makeagent/ma-next/netlify/functions---
DEBUG=true
NANGO_SECRET_KEY=
MASTRA_URL=http://makeagent.mastra.cloud
MASTRA_INTENT_AGENT_NAME=intentAgent
MASTRA_ACTIONS_AGENT_NAME=actionsAgent
#==============prod only==============
# RESEND_API_KEY=
# RESEND_AUDIENCE_ID=
#==============prod/non-deno-only==============
# SUPABASE_URL=
# SUPABASE_SERVICE_ROLE_KEY=
# SUPABASE_ANON_KEY=
#=========emcpe only===============
MCP_ENCRYPTION_KEY=
#=========iykyk otherwise unimportant===============
REQUEST_BIN_URL=
#---------------------------------


#---@makeagent/mastra---
OPENAI_API_KEY=
SUPABASE_SERVICE_ROLE_KEY=
SUPABASE_URL=
#======aToBWorkflow=========
ACTION_WEBHOOK_URL=http://127.0.0.1:54321 # https://<>.supabase.co/functions/v1/actions-webhook
#======currently unused=========
GOOGLE_GENERATIVE_AI_API_KEY=
ANTHROPIC_API_KEY=
#---------------------------------


### Prisma always looks to the root .env file
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
#---------------------------------


#---@makeagent/scripts---
NANGO_SECRET_KEY=
#=========nangoPlayground.ts===============
NANGO_CONNECTION_ID=
NANGO_PROVIDER_CONFIG_KEY=
#---------------------------------


#---@makeagent/emcpe---
PUBLIC_SUPABASE_ANON_KEY=
PUBLIC_SUPABASE_URL=http://127.0.0.1:54321 # https://<>.supabase.co
#---------------------------------



#---@makeagent/emcpe-server---
NANGO_SECRET_KEY=
SUPABASE_SERVICE_ROLE_KEY=
SUPABASE_URL=http://127.0.0.1:54321 # https://<>.supabase.co
#---------------------------------


#---@makeagent/emcpe-nango-integrations,@makeagent/makeagent-nango-integrations,@makeagent/nango-integrations---
NANGO_SECRET_KEY_DEV=
NANGO_SECRET_KEY_PROD=
#---------------------------------
