import { useEffect, useState } from 'react';
import { Activity, BarChart3 } from 'lucide-react';
import { groupProviders, getActionDefinition, getSyncDefinition, loadSyncData, loadScriptData } from '@/lib/actionData';
import { matchCondition } from '../../../ma-next/netlify/functions/_taskflow/jsonLogicUtils';
import { ProviderList } from '@/components/ProviderList';
import { ActionPanel } from '@/components/ActionPanel';
import { SyncPanel } from '@/components/SyncPanel';
import { ProviderScriptPanel } from '@/components/ProviderScriptPanel';
import { ThemeToggle } from '@/components/ThemeToggle';
import { ThemeProvider } from '@/components/ThemeProvider';
import Link from 'next/link';
import { useRouter } from 'next/router';

function ActionsInspectorContent() {
  const router = useRouter();
  const [providers] = useState(() => groupProviders());
  const [selection, setSelection] = useState<{
    provider: string;
    key: string;
    type: 'action' | 'sync' | 'provider-script';
  } | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [isExecutingScript, setIsExecutingScript] = useState(false);
  const [lastResult, setLastResult] = useState<any>(null);
  const [hasHydrated, setHasHydrated] = useState(false);

  const isLocal =
    typeof window === 'undefined'
      ? true
      : window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

  const handleActionSelect = (provider: string, action: string) => {
    setSelection({ provider, key: action, type: 'action' });
    setLastResult(null);
  };

  const handleSyncSelect = (provider: string, sync: string) => {
    setSelection({ provider, key: sync, type: 'sync' });
    setLastResult(null);
    loadSyncData(provider, sync).then(data => {
      if (data && "records" in data) setLastResult(data.records);
    });
  };

  const handleProviderScriptSelect = (provider: string) => {
    setSelection({ provider, key: 'provider-script', type: 'provider-script' });
    setLastResult(null);
    loadScriptData(provider).then(data => {
      if (data && data.results) {
        setLastResult(data);
      }
    });
  };

  const handleRunAction = async (provider: string, action: string) => {
    setSelection({ provider, key: action, type: 'action' });
    await executeAction(provider, action, {});
  };

  const handleRunSync = async (provider: string, sync: string) => {
    setSelection({ provider, key: sync, type: 'sync' });
    await listRecords(provider, sync);
  };

  const handleRunProviderScript = async (provider: string) => {
    setIsExecutingScript(true);
    setLastResult(null);

    try {
      const response = await fetch(`/api/run/${provider}`);
      const data = await response.json();

      if (data.results) {
        setLastResult({
          success: true,
          results: data.results,
          timestamp: new Date().toISOString(),
        });
      } else {
        setLastResult({
          success: false,
          error: data.error || 'Unknown error',
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error: any) {
      setLastResult({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsExecutingScript(false);
    }
  };

  const executeAction = async (
    provider: string,
    action: string,
    parameters: Record<string, any>
  ) => {
    setIsExecuting(true);
    try {
      const response = await fetch('/api/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider,
          action,
          parameters,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setLastResult({
          success: true,
          result: data.result,
          valid: data.valid,
          model: data.model,
          timestamp: new Date().toISOString(),
        });
      } else {
        setLastResult({
          success: false,
          error: data.error,
          timestamp: new Date().toISOString(),
        });
      }
    } catch (error: any) {
      setLastResult({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const listRecords = async (
    provider: string,
    sync: string,
    modifiedAfter?: string,
    connectionId?: string,
    condition?: any
  ) => {
    setIsExecuting(true);
    try {
      const syncDef = getSyncDefinition(provider, sync);
      if (!syncDef?.model) throw new Error('Unknown model');
      const response = await fetch('/api/list-records', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          provider,
          sync,
          model: syncDef.model,
          modifiedAfter,
          connectionId
        }),
      });
      const data = await response.json();
      if (data.success) {
        let records = data.result.records || data.result;
        if (Array.isArray(records) && condition) {
          records = records.filter((record: any) => matchCondition(record, condition));
        }
        setLastResult(records);
      } else {
        setLastResult({ success: false, error: data.error });
      }
    } catch (error: any) {
      setLastResult({ success: false, error: error.message });
    } finally {
      setIsExecuting(false);
    }
  };

  // Hydrate from localStorage after mounting to avoid SSR mismatch
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('ni-selection');
      if (stored) {
        try {
          setSelection(JSON.parse(stored));
        } catch {}
      }
    }
    setHasHydrated(true);
  }, []);

  // Auto-select first available item only if no stored selection was found
  useEffect(() => {
    if (hasHydrated && !selection && providers.length > 0) {
      const firstProvider = providers[0];
      if (firstProvider.syncs.length > 0) {
        handleSyncSelect(firstProvider.name, firstProvider.syncs[0].sync);
      } else if (firstProvider.actions.length > 0) {
        handleActionSelect(firstProvider.name, firstProvider.actions[0].action);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [hasHydrated, selection, providers]);

  // Save selection to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (selection) {
        localStorage.setItem('ni-selection', JSON.stringify(selection));
      } else {
        localStorage.removeItem('ni-selection');
      }
    }
  }, [selection]);

  if (!isLocal) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <p className="text-muted-foreground">Actions Inspector is only available on localhost.</p>
      </div>
    );
  }

  const currentAction =
    selection && selection.type === 'action'
      ? getActionDefinition(selection.provider, selection.key) || null
      : null;

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <header className="relative text-center space-y-2">
          <div className="absolute right-0 top-0 flex items-center space-x-2">
            <button
              onClick={() => router.push('/ui')}
              className="flex items-center space-x-1 text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              <BarChart3 className="h-4 w-4" />
              <span>UI Storyboard</span>
            </button>
            <ThemeToggle />
          </div>
          <h1 className="text-3xl font-bold flex items-center justify-center space-x-2">
            <Activity className="h-8 w-8" />
            <span>Nango Inspector</span>
          </h1>
          <p className="text-muted-foreground">Test and debug Nango action endpoints locally</p>
          <p className="text-xs text-muted-foreground">
            {providers.reduce((t, p) => t + p.actions.length, 0)} actions &amp;{' '}
            {providers.reduce((t, p) => t + p.syncs.length, 0)} syncs across {providers.length}{' '}
            providers
          </p>
        </header>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
          {/* Left Column - Provider List */}
          <div className="lg:col-span-1">
            <ProviderList
              providers={providers}
              selected={selection}
              onActionSelect={handleActionSelect}
              onSyncSelect={handleSyncSelect}
              onProviderScriptSelect={handleProviderScriptSelect}
              onRunAction={handleRunAction}
              onRunSync={handleRunSync}
              onRunProviderScript={handleRunProviderScript}
            />
          </div>

          {/* Right Column - Action Panel or Provider Script Panel */}
          <div className="lg:col-span-2">
            {selection?.type === 'provider-script' && selection ? (
              <ProviderScriptPanel
                provider={selection.provider}
                onExecute={handleRunProviderScript}
                isExecuting={isExecutingScript}
                initialSteps={lastResult?.results}
              />
            ) : selection?.type === 'sync' ? (
              <SyncPanel
                sync={
                  selection ? getSyncDefinition(selection.provider, selection.key) || null : null
                }
                onListRecords={listRecords}
                isListing={isExecuting}
                records={lastResult}
              />
            ) : (
              <ActionPanel
                action={currentAction}
                onExecute={executeAction}
                isExecuting={isExecuting}
                lastResult={lastResult}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function Home() {
  return (
    <ThemeProvider>
      <ActionsInspectorContent />
    </ThemeProvider>
  );
}
