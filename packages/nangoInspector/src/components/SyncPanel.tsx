import { useState, useEffect } from 'react';
import { Play, Database, Code, Loader2, Copy } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { SyncDefinition } from '@/types';
import { ConnectionInspector } from './ConnectionInspector';
import { RichSyncOutputs } from '../../../ma-next/src/components/rich-ui/RichSyncOutputs';

interface SyncPanelProps {
  sync: SyncDefinition | null;
  onListRecords: (
    provider: string,
    model: string,
    modifiedAfter?: string,
    connectionId?: string,
    condition?: any
  ) => Promise<void>;
  isListing: boolean;
  records: any[] | null;
}

export function SyncPanel({ sync, onListRecords, isListing, records }: SyncPanelProps) {
  const [modifiedAfter, setModifiedAfter] = useState('');
  const [conditionText, setConditionText] = useState('');
  const [selectedConnectionId, setSelectedConnectionId] = useState<string | undefined>();

  // Reset connection selection when sync changes
  useEffect(() => {
    setSelectedConnectionId(undefined);
  }, [sync]);

  if (!sync) {
    return (
      <Card className="h-full">
        <CardContent className="flex flex-col items-center justify-center py-12 text-center h-full">
          <Code className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">Select a Sync</h3>
          <p className="text-muted-foreground">
            Choose a sync from the provider list to view its records.
          </p>
        </CardContent>
      </Card>
    );
  }

  const handleList = async () => {
    let condition: any = undefined;
    if (conditionText.trim()) {
      try {
        condition = JSON.parse(conditionText);
      } catch (e) {
        alert('Invalid JSON condition');
        return;
      }
    }
    await onListRecords(
      sync.provider,
      sync.sync,
      modifiedAfter || undefined,
      selectedConnectionId,
      condition
    );
  };

  const handleCopy = () => {
    if (!records) return;
    navigator.clipboard.writeText(JSON.stringify(records, null, 2));
  };

  return (
    <div className="space-y-6">
      {/* Sync Connection */}
      <ConnectionInspector
        providerKey={sync.provider}
        selectedConnectionId={selectedConnectionId}
        onConnectionSelect={setSelectedConnectionId}
        syncKey={sync.sync}
      />

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Database className="h-4 w-4" />
            <span>
              Records - {sync.sync}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-sm text-muted-foreground">{sync.description}</p>
          <div className="space-y-2">
            <label className="text-sm font-medium">Modified After (ISO)</label>
            <Input
              value={modifiedAfter}
              onChange={e => setModifiedAfter(e.target.value)}
              placeholder="2024-01-01T00:00:00Z"
            />
          </div>
          <div className="space-y-2">
            <label className="text-sm font-medium">Condition (JSON Logic)</label>
            <Textarea
              value={conditionText}
              onChange={e => setConditionText(e.target.value)}
              placeholder='{"==":[{"var":"field"},"value"]}'
            />
          </div>
          <Button onClick={handleList} disabled={isListing} className="w-full">
            {isListing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Listing...
              </>
            ) : (
              <>
                <Play className="mr-2 h-4 w-4" />
                List Records
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {records && (
        <>
          {/* Try rich display first */}
          {RichSyncOutputs.canDisplay(sync.provider, sync.sync) &&
            records?.map(record => (
              <div key={record.id}>
                <RichSyncOutputs providerKey={sync.provider} syncKey={sync.sync} output={record} />
              </div>
            ))}

          {/* Fallback to detailed records display */}
          <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Records</span>
              <div className="flex items-center space-x-2">
                <span className="text-sm font-normal text-muted-foreground">
                  {Array.isArray(records)
                    ? `${records.length} record${records.length !== 1 ? 's' : ''}`
                    : 'Result'}
                </span>
                <button
                  onClick={handleCopy}
                  className="text-muted-foreground hover:text-primary"
                  title="Copy JSON"
                >
                  <Copy className="h-4 w-4" />
                </button>
              </div>
            </CardTitle>
          </CardHeader>
            <CardContent>
              <pre className="bg-muted p-4 rounded-md text-xs overflow-auto max-h-[1600px]">
                {JSON.stringify(records, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
