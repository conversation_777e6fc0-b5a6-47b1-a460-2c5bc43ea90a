import type { NangoAction, GmailSendEmailOutput, GmailSendEmailInput } from '../../models';
import { buildAttachmentLines } from '../utils/attachmentHelpers';

type NangoError = {
  error: {
    status: number;
    message: string;
  };
};

export default async function runAction(
  nango: NangoAction,
  input: GmailSendEmailInput
): Promise<GmailSendEmailOutput | NangoError> {
  try {
    const { to, subject, body, from, cc, bcc, attachments } = input;

    if (!to || !/@/.test(to)) {
      return {
        error: {
          status: 400,
          message: 'Input validation failed: Invalid or missing recipient email address',
        },
      };
    }

    // Build headers
    const headers = [
      `To: ${to}`,
      `Subject: ${subject}`,
      'MIME-Version: 1.0',
      ...(from ? [`From: ${from}`] : []),
      ...(cc ? [`Cc: ${cc}`] : []),
      ...(bcc ? [`Bcc: ${bcc}`] : []),
    ];

    const lines: string[] = [...headers, ''];

    // Handle content based on attachments
    if (!attachments || attachments.length === 0) {
      lines.push('Content-Type: text/plain; charset=UTF-8', '', body || '');
    } else {
      const boundary = '----=_Part_' + Math.random().toString(36).slice(2);
      lines.push(`Content-Type: multipart/mixed; boundary="${boundary}"`, '');
      lines.push(`--${boundary}`);
      lines.push('Content-Type: text/plain; charset="UTF-8"', '', body || '');
      const attachmentLines = await buildAttachmentLines(nango, attachments as any, boundary);
      lines.push(...attachmentLines, `--${boundary}--`);
    }

    // Construct and encode email
    const email = lines.join('\n');
    const base64EncodedEmail = Buffer.from(email)
      .toString('base64')
      .replace(/\+/g, '-')
      .replace(/\//g, '_')
      .replace(/=+$/, '');

    // Send email via API
    const response = await nango.proxy({
      method: 'POST',
      endpoint: '/gmail/v1/users/me/messages/send',
      data: { raw: base64EncodedEmail },
      retries: 3,
    });

    return {
      id: response.data.id,
      threadId: response.data.threadId,
      labelIds: response.data.labelIds || [],
    };
  } catch (error: any) {
    console.error('Error sending email:', error);
    const status = error?.response?.status || 500;
    const message =
      error?.response?.data?.error?.message ||
      error.message ||
      'An unknown error occurred while sending the email.';
    return { error: { status, message } };
  }
}
