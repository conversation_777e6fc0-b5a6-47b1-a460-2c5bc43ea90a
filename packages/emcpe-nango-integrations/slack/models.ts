// ---------------------------
// This file was generated by <PERSON><PERSON> (v0.59.7)
// It's recommended to version this file
// https://nango.dev
// ---------------------------

// ------ Models
export interface UrlAuthentication {
  providerKey: string;
  connectionId: string;
}

export interface UrlAccessibleFile {
  url: string;
  authentication: UrlAuthentication;
}
export interface SlackBlock {
  type: string;
  block_id?: string;
  text?: Record<string, any>;
  elements?: Record<string, any>[];
  fields?: Record<string, any>[];
  accessory?: Record<string, any>;
}

export interface SlackAttachment {
  id?: number;
  fallback?: string;
  color?: string;
  pretext?: string;
  author_name?: string;
  author_link?: string;
  author_icon?: string;
  title?: string;
  title_link?: string;
  text?: string;
  fields?: Record<string, any>[];
  image_url?: string;
  thumb_url?: string;
  footer?: string;
  footer_icon?: string;
  ts?: number;
}

export interface SlackFile {
  id: string;
  name?: string;
  filetype?: string;
  url_private?: string;
  url_private_download?: string;
  mimetype?: string;
  size?: number;
  title?: string;
  created?: number;
  timestamp?: number;
  user?: string;
  editable?: boolean;
  mode?: string;
  is_external?: boolean;
  external_type?: string;
  permalink?: string;
  preview?: string;
  accessible?: UrlAccessibleFile;
}

export interface SlackReaction {
  name: string;
  count: number;
  users: string[];
}

export interface SlackEdited {
  user: string;
  ts: string;
}

export interface SlackMessage {
  type: string;
  subtype?: string;
  ts: string;
  user?: string;
  text: string;
  thread_ts?: string;
  reply_count?: number;
  blocks?: SlackBlock[];
  attachments?: SlackAttachment[];
  files?: SlackFile[];
  reactions?: SlackReaction[];
  parent_user_id?: string;
  edited?: SlackEdited;
  bot_id?: string;
  icons?: Record<string, any>;
  team?: string;
  app_id?: string;
  client_msg_id?: string;
}

export interface SlackSyncMessage {
  id: string;
  channel_id: string;
  message: SlackMessage;
}

export interface SlackSendMessageInput {
  channel: string;
  text: string;
  thread_ts?: string;
}

export interface SlackSendMessageOutput {
  ok: boolean;
  ts: string;
  channel: string;
  message_text?: string;
}

export interface SlackListChannelsInput {
  types?: string;
  limit?: number;
  cursor?: string;
}

export interface SlackScopeSyncMetadata {
  conversations?: string[];
}

export interface SlackConversation {
  id: string;
  name?: string;
  is_channel?: boolean;
  is_group?: boolean;
  is_im?: boolean;
  is_mpim?: boolean;
  is_private?: boolean;
  is_member?: boolean;
  user?: string;
  num_members?: number;
}

export interface SlackResponseMetadata {
  next_cursor?: string;
}

export interface SlackConversationsList {
  ok: boolean;
  channels: SlackConversation[];
  response_metadata?: SlackResponseMetadata;
  error?: string;
}

export interface SlackGetChannelHistoryInput {
  channel: string;
  limit?: number;
  latest?: string;
  oldest?: string;
  cursor?: string;
}

export interface SlackMessageList {
  ok: boolean;
  messages: SlackMessage[];
  has_more?: boolean;
  pin_count?: number;
  channel_actions_ts?: string;
  channel_actions_count?: number;
  response_metadata?: SlackResponseMetadata;
  error?: string;
}

export interface SlackGetUserInfoInput {
  user: string;
}

export interface SlackUserProfile {
  real_name?: string;
  display_name?: string;
  email?: string;
  image_original?: string;
  image_512?: string;
}

export interface SlackUserInfo {
  id: string;
  name: string;
  is_bot: boolean;
  is_admin?: boolean;
  is_owner?: boolean;
  tz?: string;
  profile?: SlackUserProfile;
}

export interface SlackAddReactionInput {
  name: string;
  channel: string;
  timestamp: string;
}

export interface SlackReactionOutput {
  ok: boolean;
  error?: string;
}

export interface SlackSearchMessagesInput {
  query: string;
  sort?: string;
  sort_dir?: string;
  count?: number;
  page?: number;
}

export interface SlackSearchMatch {
  iid: string;
  team: string;
  channel: Record<string, any>;
  type: string;
  user: string;
  username: string;
  ts: string;
  text: string;
  permalink: string;
}

export interface SlackSearchResultList {
  ok: boolean;
  query: string;
  messages: Record<string, any>;
  error?: string;
}

export interface SlackGetPermalinkInput {
  channel: string;
  message_ts: string;
}

export interface SlackPermalinkOutput {
  ok: boolean;
  permalink?: string;
  channel?: string;
  error?: string;
}

export interface SlackUpdateMessageInput {
  channel: string;
  ts: string;
  text: string;
}

export interface SlackUpdateMessageOutput {
  ok: boolean;
  channel?: string;
  ts?: string;
  text?: string;
  error?: string;
}

export interface SlackDeleteMessageInput {
  channel: string;
  ts: string;
}

export interface SlackDeleteMessageOutput {
  ok: boolean;
  channel?: string;
  ts?: string;
  error?: string;
}
// ------ /Models

// ------ SDK

import type { Nango } from '@nangohq/node';
import type {
  AxiosInstance,
  AxiosInterceptorManager,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import type {
  ApiEndUser,
  DBSyncConfig,
  DBTeam,
  GetPublicIntegration,
  HTTP_METHOD,
  RunnerFlags,
  PostPublicTrigger,
} from '@nangohq/types';
import type { ZodSchema, SafeParseSuccess } from 'zod';

export declare const oldLevelToNewLevel: {
  readonly debug: 'debug';
  readonly info: 'info';
  readonly warn: 'warn';
  readonly error: 'error';
  readonly verbose: 'debug';
  readonly silly: 'debug';
  readonly http: 'info';
};
type LogLevel = 'info' | 'debug' | 'error' | 'warn' | 'http' | 'verbose' | 'silly';
interface Pagination {
  type: string;
  limit?: number | string;
  response_path?: string;
  limit_name_in_request: string;
  in_body?: boolean;
  on_page?: (paginationState: {
    nextPageParam?: string | number | undefined;
    response: AxiosResponse;
  }) => Promise<void>;
}
interface CursorPagination extends Pagination {
  cursor_path_in_response: string;
  cursor_name_in_request: string;
}
interface LinkPagination extends Pagination {
  link_rel_in_response_header?: string;
  link_path_in_response_body?: string;
}
interface OffsetPagination extends Pagination {
  offset_name_in_request: string;
  offset_start_value?: number;
  offset_calculation_method?: 'per-page' | 'by-response-size';
}
interface RetryHeaderConfig {
  at?: string;
  after?: string;
}
export interface ProxyConfiguration {
  endpoint: string;
  providerConfigKey?: string;
  connectionId?: string;
  method?:
    | 'GET'
    | 'POST'
    | 'PATCH'
    | 'PUT'
    | 'DELETE'
    | 'get'
    | 'post'
    | 'patch'
    | 'put'
    | 'delete';
  headers?: Record<string, string>;
  params?: string | Record<string, string | number>;
  data?: unknown;
  retries?: number;
  baseUrlOverride?: string;
  paginate?: Partial<CursorPagination> | Partial<LinkPagination> | Partial<OffsetPagination>;
  retryHeader?: RetryHeaderConfig;
  responseType?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text' | 'stream' | undefined;
  retryOn?: number[] | null;
}
export interface AuthModes {
  OAuth1: 'OAUTH1';
  OAuth2: 'OAUTH2';
  OAuth2CC: 'OAUTH2_CC';
  Basic: 'BASIC';
  ApiKey: 'API_KEY';
  AppStore: 'APP_STORE';
  Custom: 'CUSTOM';
  App: 'APP';
  None: 'NONE';
  TBA: 'TBA';
  Tableau: 'TABLEAU';
  Jwt: 'JWT';
  Bill: 'BILL';
  TwoStep: 'TWO_STEP';
  Signature: 'SIGNATURE';
}
export type AuthModeType = AuthModes[keyof AuthModes];
interface OAuth1Token {
  oAuthToken: string;
  oAuthTokenSecret: string;
}
interface AppCredentials {
  type: AuthModes['App'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
}
interface AppStoreCredentials {
  type?: AuthModes['AppStore'];
  access_token: string;
  expires_at?: Date | undefined;
  raw: Record<string, any>;
  private_key: string;
}
interface BasicApiCredentials {
  type: AuthModes['Basic'];
  username: string;
  password: string;
}
interface ApiKeyCredentials {
  type: AuthModes['ApiKey'];
  apiKey: string;
}
interface CredentialsCommon<T = Record<string, any>> {
  type: AuthModeType;
  raw: T;
}
interface OAuth2Credentials extends CredentialsCommon {
  type: AuthModes['OAuth2'];
  access_token: string;
  refresh_token?: string;
  expires_at?: Date | undefined;
}
interface OAuth2ClientCredentials extends CredentialsCommon {
  type: AuthModes['OAuth2CC'];
  token: string;
  expires_at?: Date | undefined;
  client_id: string;
  client_secret: string;
}
interface OAuth1Credentials extends CredentialsCommon {
  type: AuthModes['OAuth1'];
  oauth_token: string;
  oauth_token_secret: string;
}
interface TbaCredentials {
  type: AuthModes['TBA'];
  token_id: string;
  token_secret: string;
  config_override: {
    client_id?: string;
    client_secret?: string;
  };
}
interface TableauCredentials extends CredentialsCommon {
  type: AuthModes['Tableau'];
  pat_name: string;
  pat_secret: string;
  content_url?: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface JwtCredentials {
  type: AuthModes['Jwt'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface BillCredentials extends CredentialsCommon {
  type: AuthModes['Bill'];
  username: string;
  password: string;
  organization_id: string;
  dev_key: string;
  session_id?: string;
  user_id?: string;
  expires_at?: Date | undefined;
}
interface TwoStepCredentials extends CredentialsCommon {
  type: AuthModes['TwoStep'];
  [key: string]: any;
  token?: string;
  expires_at?: Date | undefined;
}
interface SignatureCredentials {
  type: AuthModes['Signature'];
  username: string;
  password: string;
  token?: string;
  expires_at?: Date | undefined;
}
interface CustomCredentials extends CredentialsCommon {
  type: AuthModes['Custom'];
}
type UnauthCredentials = Record<string, never>;
type AuthCredentials =
  | OAuth2Credentials
  | OAuth2ClientCredentials
  | OAuth1Credentials
  | BasicApiCredentials
  | ApiKeyCredentials
  | AppCredentials
  | AppStoreCredentials
  | UnauthCredentials
  | TbaCredentials
  | TableauCredentials
  | JwtCredentials
  | BillCredentials
  | TwoStepCredentials
  | SignatureCredentials
  | CustomCredentials;
type Metadata = Record<string, unknown>;
interface MetadataChangeResponse {
  metadata: Metadata;
  provider_config_key: string;
  connection_id: string | string[];
}
interface Connection {
  id: number;
  provider_config_key: string;
  connection_id: string;
  connection_config: Record<string, string>;
  created_at: string;
  updated_at: string;
  last_fetched_at: string;
  metadata: Record<string, unknown> | null;
  provider: string;
  errors: {
    type: string;
    log_id: string;
  }[];
  end_user: ApiEndUser | null;
  credentials: AuthCredentials;
}
export declare class ActionError<T = Record<string, unknown>> extends Error {
  type: string;
  payload?: Record<string, unknown>;
  constructor(payload?: T);
}
export interface NangoProps {
  scriptType: 'sync' | 'action' | 'webhook' | 'on-event';
  host?: string;
  secretKey: string;
  team?: Pick<DBTeam, 'id' | 'name'>;
  connectionId: string;
  environmentId: number;
  environmentName?: string;
  activityLogId?: string | undefined;
  providerConfigKey: string;
  provider: string;
  lastSyncDate?: Date;
  syncId?: string | undefined;
  nangoConnectionId?: number;
  syncJobId?: number | undefined;
  dryRun?: boolean;
  track_deletes?: boolean;
  attributes?: object | undefined;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]> | undefined;
  rawDeleteOutput?: Map<string, unknown[]> | undefined;
  stubbedMetadata?: Metadata | undefined;
  abortSignal?: AbortSignal;
  syncConfig: DBSyncConfig;
  runnerFlags: RunnerFlags;
  debug: boolean;
  startedAt: Date;
  endUser: {
    id: number;
    endUserId: string | null;
    orgId: string | null;
  } | null;
  axios?: {
    request?: AxiosInterceptorManager<AxiosRequestConfig>;
    response?: {
      onFulfilled: (value: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>;
      onRejected: (value: unknown) => AxiosError | Promise<AxiosError>;
    };
  };
}
export interface EnvironmentVariable {
  name: string;
  value: string;
}
export declare const defaultPersistApi: AxiosInstance;
export declare class NangoAction {
  protected nango: Nango;
  private attributes;
  protected persistApi: AxiosInstance;
  activityLogId?: string | undefined;
  syncId?: string;
  nangoConnectionId?: number;
  environmentId: number;
  environmentName?: string;
  syncJobId?: number;
  dryRun?: boolean;
  abortSignal?: AbortSignal;
  syncConfig?: DBSyncConfig;
  runnerFlags: RunnerFlags;
  connectionId: string;
  providerConfigKey: string;
  provider?: string;
  ActionError: typeof ActionError;
  private memoizedConnections;
  private memoizedIntegration;
  constructor(
    config: NangoProps,
    {
      persistApi,
    }?: {
      persistApi: AxiosInstance;
    }
  );
  protected stringify(): string;
  private proxyConfig;
  protected throwIfAborted(): void;
  proxy<T = any>(config: ProxyConfiguration): Promise<AxiosResponse<T>>;
  get<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  post<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  put<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  patch<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  delete<T = any>(config: Omit<ProxyConfiguration, 'method'>): Promise<AxiosResponse<T>>;
  getToken(): Promise<
    | string
    | OAuth1Token
    | OAuth2ClientCredentials
    | BasicApiCredentials
    | ApiKeyCredentials
    | AppCredentials
    | AppStoreCredentials
    | UnauthCredentials
    | CustomCredentials
    | TbaCredentials
    | TableauCredentials
    | JwtCredentials
    | BillCredentials
    | TwoStepCredentials
    | SignatureCredentials
  >;
  /**
   * Get current integration
   */
  getIntegration(
    queries?: GetPublicIntegration['Querystring']
  ): Promise<GetPublicIntegration['Success']['data']>;
  getConnection(
    providerConfigKeyOverride?: string,
    connectionIdOverride?: string
  ): Promise<Connection>;
  setMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  updateMetadata(metadata: Metadata): Promise<AxiosResponse<MetadataChangeResponse>>;
  /**
   * @deprecated please use setMetadata instead.
   */
  setFieldMapping(fieldMapping: Record<string, string>): Promise<AxiosResponse<object>>;
  getMetadata<T = Metadata>(): Promise<T>;
  getWebhookURL(): Promise<string | null | undefined>;
  /**
   * @deprecated please use getMetadata instead.
   */
  getFieldMapping(): Promise<Metadata>;
  /**
   * Log
   * @desc Log a message to the activity log which shows up in the Nango Dashboard
   * note that the last argument can be an object with a level property to specify the log level
   * @example
   * ```ts
   * await nango.log('This is a log message', { level: 'error' })
   * ```
   */
  log(
    message: any,
    options?:
      | {
          level?: LogLevel;
        }
      | {
          [key: string]: any;
          level?: never;
        }
  ): Promise<void>;
  log(
    message: string,
    ...args: [
      any,
      {
        level?: LogLevel;
      },
    ]
  ): Promise<void>;
  getEnvironmentVariables(): Promise<EnvironmentVariable[] | null>;
  getFlowAttributes<A = object>(): A | null;
  paginate<T = any>(config: ProxyConfiguration): AsyncGenerator<T[], undefined, void>;
  triggerAction<In = unknown, Out = object>(
    providerConfigKey: string,
    connectionId: string,
    actionName: string,
    input?: In
  ): Promise<Out>;
  zodValidateInput<T = any, Z = any>({
    zodSchema,
    input,
  }: {
    zodSchema: ZodSchema<Z>;
    input: T;
  }): Promise<SafeParseSuccess<Z>>;
  triggerSync(
    providerConfigKey: string,
    connectionId: string,
    sync: string | { name: string; variant: string },
    syncMode?: PostPublicTrigger['Body']['sync_mode'] | boolean
  ): Promise<void | string>;
  startSync(
    providerConfigKey: string,
    syncs: (string | { name: string; variant: string })[],
    connectionId?: string
  ): Promise<void>;
  /**
   * Uncontrolled fetch is a regular fetch without retry or credentials injection.
   * Only use that method when you want to access resources that are unrelated to the current connection/provider.
   */
  uncontrolledFetch(options: {
    url: URL;
    method?: HTTP_METHOD;
    headers?: Record<string, string> | undefined;
    body?: string | null;
  }): Promise<Response>;
  tryAcquireLock(props: { key: string; ttlMs: number }): Promise<boolean>;
  releaseLock(props: { key: string }): Promise<boolean>;
  private sendLogToPersist;
  private logAPICall;
}
export declare class NangoSync extends NangoAction {
  variant: string;
  lastSyncDate?: Date;
  track_deletes: boolean;
  logMessages?:
    | {
        counts: {
          updated: number;
          added: number;
          deleted: number;
        };
        messages: unknown[];
      }
    | undefined;
  rawSaveOutput?: Map<string, unknown[]>;
  rawDeleteOutput?: Map<string, unknown[]>;
  stubbedMetadata?: Metadata | undefined;
  private batchSize;
  constructor(config: NangoProps);
  /**
   * @deprecated please use batchSave
   */
  batchSend<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchSave<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchDelete<T extends object>(results: T[], model: string): Promise<boolean | null>;
  batchUpdate<T extends object>(results: T[], model: string): Promise<boolean | null>;
  getMetadata<T = Metadata>(): Promise<T>;
  setMergingStrategy(
    merging: { strategy: 'ignore_if_modified_after' | 'override' },
    model: string
  ): Promise<void>;
  getRecordsByIds<K = string | number, T = any>(ids: K[], model: string): Promise<Map<K, T>>;
}
/**
 * @internal
 *
 * This function will enable tracing on the SDK
 * It has been split from the actual code to avoid making the code too dirty and to easily enable/disable tracing if there is an issue with it
 */
export declare function instrumentSDK(rawNango: NangoAction | NangoSync): NangoAction | NangoSync;
export {};

// ------ /SDK

// ------ Flows
export const NangoFlows = [
  {
    providerConfigKey: 'slack',
    syncs: [
      {
        name: 'messages',
        type: 'sync',
        description:
          'Syncs messages from all channels the user can access, including replies.\nCursors are stored per channel in metadata for incremental syncing.',
        sync_type: 'incremental',
        usedModels: [
          'SlackSyncMessage',
          'SlackMessage',
          'SlackBlock',
          'SlackAttachment',
          'SlackFile',
          'SlackReaction',
          'SlackEdited',
          'SlackScopeSyncMetadata',
        ],
        runs: 'every 1 minutes',
        version: '1.0.0',
        track_deletes: false,
        auto_start: false,
        input: 'SlackScopeSyncMetadata',
        output: ['SlackSyncMessage'],
        scopes: [
          'channels:read',
          'groups:read',
          'im:read',
          'mpim:read',
          'channels:history',
          'groups:history',
          'im:history',
          'mpim:history',
        ],
        endpoints: [
          {
            method: 'GET',
            path: '/messages',
            group: 'Messages',
          },
        ],
        webhookSubscriptions: [],
      },
    ],
    actions: [
      {
        name: 'send-message-as-user',
        type: 'action',
        description: 'Sends a message to a Slack channel as the authenticated user.',
        version: '',
        scopes: ['chat:write'],
        input: 'SlackSendMessageInput',
        output: ['SlackSendMessageOutput'],
        usedModels: ['SlackSendMessageOutput', 'SlackSendMessageInput'],
        endpoint: {
          method: 'POST',
          path: '/chat.postMessage',
        },
      },
      {
        name: 'list-channels',
        type: 'action',
        description: 'Lists channels in Slack.',
        version: '',
        scopes: ['channels:read', 'groups:read', 'im:read', 'mpim:read'],
        input: 'SlackListChannelsInput',
        output: ['SlackConversationsList'],
        usedModels: [
          'SlackConversationsList',
          'SlackConversation',
          'SlackResponseMetadata',
          'SlackListChannelsInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/conversations.list',
        },
      },
      {
        name: 'get-channel-history',
        type: 'action',
        description: 'Retrieves message history for a specific channel.',
        version: '',
        scopes: ['channels:history', 'groups:history', 'im:history', 'mpim:history'],
        input: 'SlackGetChannelHistoryInput',
        output: ['SlackMessageList'],
        usedModels: [
          'SlackMessageList',
          'SlackMessage',
          'SlackBlock',
          'SlackAttachment',
          'SlackFile',
          'SlackReaction',
          'SlackEdited',
          'SlackResponseMetadata',
          'SlackGetChannelHistoryInput',
        ],
        endpoint: {
          method: 'GET',
          path: '/conversations.history',
        },
      },
      {
        name: 'get-user-info',
        type: 'action',
        description: 'Retrieves information about a specific user.',
        version: '',
        scopes: ['users:read'],
        input: 'SlackGetUserInfoInput',
        output: ['SlackUserInfo'],
        usedModels: ['SlackUserInfo', 'SlackUserProfile', 'SlackGetUserInfoInput'],
        endpoint: {
          method: 'GET',
          path: '/users.info',
        },
      },
      {
        name: 'add-reaction-as-user',
        type: 'action',
        description: 'Adds an emoji reaction to a message as the authenticated user.',
        version: '',
        scopes: ['reactions:write'],
        input: 'SlackAddReactionInput',
        output: ['SlackReactionOutput'],
        usedModels: ['SlackReactionOutput', 'SlackAddReactionInput'],
        endpoint: {
          method: 'POST',
          path: '/reactions.add',
        },
      },
      {
        name: 'search-messages',
        type: 'action',
        description: 'Searches for messages matching a query.',
        version: '',
        scopes: ['search:read'],
        input: 'SlackSearchMessagesInput',
        output: ['SlackSearchResultList'],
        usedModels: ['SlackSearchResultList', 'SlackSearchMessagesInput'],
        endpoint: {
          method: 'GET',
          path: '/search.messages',
        },
      },
      {
        name: 'get-message-permalink',
        type: 'action',
        description: 'Retrieves a permalink for a specific message.',
        version: '',
        scopes: ['chat:read'],
        input: 'SlackGetPermalinkInput',
        output: ['SlackPermalinkOutput'],
        usedModels: ['SlackPermalinkOutput', 'SlackGetPermalinkInput'],
        endpoint: {
          method: 'GET',
          path: '/chat.getPermalink',
        },
      },
      {
        name: 'update-message-as-user',
        type: 'action',
        description: 'Updates an existing message in a channel as the authenticated user.',
        version: '',
        scopes: ['chat:write'],
        input: 'SlackUpdateMessageInput',
        output: ['SlackUpdateMessageOutput'],
        usedModels: ['SlackUpdateMessageOutput', 'SlackUpdateMessageInput'],
        endpoint: {
          method: 'POST',
          path: '/chat.update',
        },
      },
      {
        name: 'delete-message-as-user',
        type: 'action',
        description: 'Deletes a message from a channel as the authenticated user.',
        version: '',
        scopes: ['chat:write'],
        input: 'SlackDeleteMessageInput',
        output: ['SlackDeleteMessageOutput'],
        usedModels: ['SlackDeleteMessageOutput', 'SlackDeleteMessageInput'],
        endpoint: {
          method: 'POST',
          path: '/chat.delete',
        },
      },
    ],
    onEventScripts: {
      'post-connection-creation': [],
      'pre-connection-deletion': [],
    },
  },
] as const;
// ------ /Flows
