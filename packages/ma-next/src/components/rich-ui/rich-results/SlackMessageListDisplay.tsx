import React, { useMemo } from 'react';
import { MessageSquare } from 'lucide-react';
import { SlackMessageList } from 'src/config/nangoModels';
import { slackUsersStore } from 'components/rich-ui/rich-results/shared/slackUsers';
import { SlackMessage, extractUserIds } from './shared/SlackMessage';

interface SlackMessageListDisplayProps {
  output: SlackMessageList;
}

/**
 * Renders a rich display of Slack channel message history
 */
function SlackMessageListDisplay({ output }: SlackMessageListDisplayProps) {
  const data = output;

  const idsFromUserField =
    data?.messages?.map(m => m.user).filter((id): id is string => Boolean(id)) || [];
  const idsFromText = data?.messages?.flatMap(m => (m.text ? extractUserIds(m.text) : [])) || [];
  const uniqueIds = useMemo(
    () => Array.from(new Set([...idsFromUserField, ...idsFromText])),
    [output]
  );

  const users = slackUsersStore.useSlackUsers(uniqueIds);

  // Check if we have valid data
  if (!data || !data.messages || data.messages.length === 0) {
    return (
      <div className="p-6 text-center">
        <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No messages found</p>
      </div>
    );
  }

  const messages = data.messages;
  const hasMore = data.has_more;
  const nextCursor = data.response_metadata?.next_cursor;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Messages ({messages.length})
          </h3>
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        {messages.map((message, index) => (
          <SlackMessage
            key={message.ts || index}
            message={message}
            users={users}
            index={index}
          />
        ))}
      </div>

      {hasMore && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            More messages available. {nextCursor ? '' : ''}
          </p>
        </div>
      )}
    </div>
  );
}

export { SlackMessageListDisplay };
