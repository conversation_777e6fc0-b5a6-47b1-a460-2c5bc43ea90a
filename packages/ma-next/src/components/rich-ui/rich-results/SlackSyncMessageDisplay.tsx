import React, { useMemo } from 'react';
import { MessageSquare, Hash } from 'lucide-react';
import { SlackSyncMessage } from 'src/config/nangoModels';
import { slackUsersStore } from 'components/rich-ui/rich-results/shared/slackUsers';
import { SlackMessage, extractUserIds } from './shared/SlackMessage';

interface SlackSyncMessageDisplayProps {
  output: SlackSyncMessage;
  actionParameters?: Record<string, any>;
}

/**
 * Renders a rich display of a single Slack sync message
 */
function SlackSyncMessageDisplay({ output, actionParameters }: SlackSyncMessageDisplayProps) {
  const data = output;

  const idsFromUserField = data?.message?.user ? [data.message.user] : [];
  const idsFromText = data?.message?.text ? extractUserIds(data.message.text) : [];
  const uniqueIds = useMemo(
    () => Array.from(new Set([...idsFromUser<PERSON>ield, ...idsFromText])),
    [output]
  );

  const users = slackUsersStore.useSlackUsers(uniqueIds);

  // Check if we have valid data
  if (!data || !data.message) {
    return (
      <div className="p-6 text-center">
        <MessageSquare className="w-12 h-12 mx-auto text-gray-400 dark:text-gray-600 mb-3" />
        <p className="text-gray-500 dark:text-gray-400">No message data available</p>
      </div>
    );
  }

  const channelName = actionParameters?.channel || data.channel_id;

  return (
    <div>
      <div className="bg-gray-50 dark:bg-gray-800 px-5 py-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center">
          <MessageSquare className="w-5 h-5 text-purple-600 dark:text-purple-400 mr-2" />
          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
            Slack Message
          </h3>
          {channelName && (
            <div className="ml-auto flex items-center text-xs text-gray-500 dark:text-gray-400">
              <Hash className="w-3 h-3 mr-1" />
              <span>{channelName}</span>
            </div>
          )}
        </div>
      </div>

      <div className="divide-y divide-gray-200 dark:divide-gray-700">
        <SlackMessage
          message={data.message}
          users={users}
        />
      </div>

      {/* Message metadata */}
      <div className="p-4 bg-gray-50 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
          <div>Message ID: {data.id}</div>
          <div>Channel ID: {data.channel_id}</div>
          <div>Timestamp: {data.message.ts}</div>
        </div>
      </div>
    </div>
  );
}

export { SlackSyncMessageDisplay };
