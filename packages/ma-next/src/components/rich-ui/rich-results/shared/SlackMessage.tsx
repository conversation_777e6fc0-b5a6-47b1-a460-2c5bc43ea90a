import React from 'react';
import {
  User,
  Clock,
  Paperclip,
  Download,
  MoreHorizontal,
  ExternalLink,
} from 'lucide-react';
import { format } from 'date-fns';
import { SlackMessage as SlackMessageType, SlackUserInfo, SlackFile } from 'src/config/nangoModels';
import { DropdownMenu } from '../../common/DropdownMenu';
import { ContextMenuItem } from '../../common/ContextMenu';

// Extract all user IDs mentioned in a Slack formatted text string
function extractUserIds(text: string): string[] {
  const ids: string[] = [];
  const regex = /<@([A-Z0-9]+)(?:\|[^>]+)?>/g;
  let match;
  while ((match = regex.exec(text))) {
    ids.push(match[1]);
  }
  return ids;
}

// Replace user mention IDs in a Slack text string with display names
function replaceUserMentions(text: string, userMap: Record<string, SlackUserInfo>): string {
  return text.replace(/<@([A-Z0-9]+)(?:\|[^>]+)?>/g, (_full, id) => {
    const info = userMap[id];
    const name = info?.profile?.display_name || info?.profile?.real_name || info?.name || id;
    return `@${name}`;
  });
}

interface SlackMessageProps {
  message: SlackMessageType;
  users: Record<string, SlackUserInfo>;
  index?: number;
}

/**
 * Renders a single Slack message with user info, timestamp, and attachments
 */
function SlackMessage({ message, users, index = 0 }: SlackMessageProps) {
  const userInfo = message.user ? users[message.user] : undefined;
  const displayName =
    userInfo?.profile?.display_name ||
    userInfo?.profile?.real_name ||
    userInfo?.name ||
    message.user;
  const avatar = userInfo?.profile?.image_512 || userInfo?.profile?.image_original;
  
  // Format timestamp
  let formattedTime = '';
  try {
    // Slack timestamps are in seconds, JS Date expects milliseconds
    const timestamp = parseFloat(message.ts) * 1000;
    formattedTime = format(new Date(timestamp), 'MMM d, yyyy h:mm a');
  } catch {
    formattedTime = message.ts;
  }

  return (
    <div
      key={message.ts || index}
      className="p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
    >
      <div className="flex items-start">
        <div className="flex-shrink-0 mr-3">
          {avatar ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img src={avatar} alt={displayName} className="w-9 h-9 rounded-full" />
          ) : (
            <div className="w-9 h-9 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
              <User className="w-4 h-4 text-purple-600 dark:text-purple-400" />
            </div>
          )}
        </div>
        <div className="min-w-0 flex-1">
          {/* User and timestamp */}
          <div className="flex items-center mb-1">
            {displayName && (
              <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mr-2">
                {displayName}
              </div>
            )}
            <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
              <Clock className="w-3 h-3 mr-1" />
              <span>{formattedTime}</span>
            </div>
            {message.thread_ts && message.reply_count && (
              <div className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                {message.reply_count} {message.reply_count === 1 ? 'reply' : 'replies'}
              </div>
            )}
          </div>

          {/* Message text */}
          <p className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-line">
            {replaceUserMentions(message.text, users)}
          </p>

          {/* File attachments */}
          {message.files && message.files.length > 0 && (
            <SlackFileAttachments files={message.files} />
          )}

          {/* Thread indicator */}
          {message.thread_ts && !message.reply_count && (
            <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">In thread</div>
          )}
        </div>
      </div>
    </div>
  );
}

// Component for displaying file attachments
interface SlackFileAttachmentsProps {
  files: SlackFile[];
}

function SlackFileAttachments({ files }: SlackFileAttachmentsProps) {
  // Handle file thumbnail - try to get thumbnail URL
  const getThumbnailUrl = (file: SlackFile) => {
    return file.thumb_url || null;
  };

  // Handle file download - open Slack link directly
  const handleDownload = (file: SlackFile) => {
    if (file.url_private_download) {
      window.open(file.url_private_download, '_blank');
    } else if (file.url_private) {
      window.open(file.url_private, '_blank');
    }
  };

  // Handle file view - open Slack link directly
  const handleView = (file: SlackFile) => {
    if (file.url_private) {
      window.open(file.url_private, '_blank');
    }
  };

  return (
    <div className="mt-3 space-y-2">
      {files.map(file => {
        const menuItems: ContextMenuItem[] = [
          {
            label: 'View in Slack',
            onClick: () => handleView(file),
            icon: <ExternalLink className="w-4 h-4 mr-2" />,
          },
          {
            label: 'Download',
            onClick: () => handleDownload(file),
            icon: <Download className="w-4 h-4 mr-2" />,
          },
        ];

        return (
          <div
            key={file.id}
            className="flex items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
          >
            {/* File icon */}
            <div className="flex-shrink-0 mr-3">
              <div className="w-12 h-12 rounded bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <Paperclip className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>

            {/* File details */}
            <div className="min-w-0 flex-1">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {file.name || file.title || 'Untitled file'}
                </h4>

                {/* Three-dot menu */}
                <DropdownMenu
                  trigger={
                    <button
                      className="p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ml-2"
                      title="More options"
                    >
                      <MoreHorizontal className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                    </button>
                  }
                  items={menuItems}
                />
              </div>

              <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-1">
                {file.filetype && (
                  <span className="uppercase mr-2">{file.filetype}</span>
                )}
                {file.size && (
                  <span>{(file.size / 1024).toFixed(1)} KB</span>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}

export { SlackMessage, extractUserIds, replaceUserMentions };
